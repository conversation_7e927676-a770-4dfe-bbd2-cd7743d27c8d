package migrations

import (
	"database/sql"
	"fmt"

	"github.com/pressly/goose/v3"
)

func init() {
	goose.AddMigration(upMigrateFromLegacy, downMigrateFromLegacy)
}

// upMigrateFromLegacy 从旧迁移系统迁移到Goose
func upMigrateFromLegacy(tx *sql.Tx) error {
	// 检查是否存在旧的migration_test表（003版本的标志）
	var testTableExists int
	err := tx.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'migration_test'").Scan(&testTableExists)
	if err != nil {
		return fmt.Errorf("failed to check migration_test table: %w", err)
	}

	// 如果migration_test表不存在，说明003版本未执行，需要创建
	if testTableExists == 0 {
		// 创建测试表（对应旧系统003版本）
		_, err = tx.Exec(`
			CREATE TABLE IF NOT EXISTS migration_test (
				id int(11) NOT NULL AUTO_INCREMENT,
				test_field varchar(255) NOT NULL DEFAULT '',
				created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
				PRIMARY KEY (id)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
		`)
		if err != nil {
			return fmt.Errorf("failed to create migration_test table: %w", err)
		}

		// 插入测试数据
		_, err = tx.Exec(`
			INSERT INTO migration_test (test_field) VALUES 
			('Migration 003 executed via Goose'),
			('Legacy migration compatibility')
		`)
		if err != nil {
			return fmt.Errorf("failed to insert test data: %w", err)
		}
	}

	// 检查services表是否存在（验证002版本是否已执行）
	var servicesTableExists int
	err = tx.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'services'").Scan(&servicesTableExists)
	if err != nil {
		return fmt.Errorf("failed to check services table: %w", err)
	}

	// 如果services表仍然存在，说明002版本未执行，需要删除
	if servicesTableExists > 0 {
		// 备份services表数据
		_, err = tx.Exec("CREATE TABLE IF NOT EXISTS services_backup_goose AS SELECT * FROM services")
		if err != nil {
			return fmt.Errorf("failed to backup services table: %w", err)
		}

		// 删除services表
		_, err = tx.Exec("DROP TABLE IF EXISTS services")
		if err != nil {
			return fmt.Errorf("failed to drop services table: %w", err)
		}
	}

	// 创建迁移状态记录表（用于跟踪旧系统迁移状态）
	_, err = tx.Exec(`
		CREATE TABLE IF NOT EXISTS legacy_migration_status (
			id int(11) NOT NULL AUTO_INCREMENT,
			version varchar(10) NOT NULL,
			description varchar(255) NOT NULL,
			executed_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
			migrated_to_goose tinyint(1) DEFAULT 1,
			PRIMARY KEY (id),
			UNIQUE KEY uk_version (version)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
	`)
	if err != nil {
		return fmt.Errorf("failed to create legacy_migration_status table: %w", err)
	}

	// 记录已完成的旧版本迁移
	_, err = tx.Exec(`
		INSERT IGNORE INTO legacy_migration_status (version, description) VALUES
		('001', 'Optimize table structure - migrated to Goose baseline'),
		('002', 'Drop services table - migrated to Goose baseline'),
		('003', 'Test migration - migrated to Goose 00003')
	`)
	if err != nil {
		return fmt.Errorf("failed to insert legacy migration records: %w", err)
	}

	return nil
}

// downMigrateFromLegacy 回滚旧系统迁移
func downMigrateFromLegacy(tx *sql.Tx) error {
	// 删除迁移状态记录表
	_, err := tx.Exec("DROP TABLE IF EXISTS legacy_migration_status")
	if err != nil {
		return fmt.Errorf("failed to drop legacy_migration_status table: %w", err)
	}

	// 删除测试表
	_, err = tx.Exec("DROP TABLE IF EXISTS migration_test")
	if err != nil {
		return fmt.Errorf("failed to drop migration_test table: %w", err)
	}

	// 如果存在services备份，恢复services表
	var backupExists int
	err = tx.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'services_backup_goose'").Scan(&backupExists)
	if err != nil {
		return fmt.Errorf("failed to check services backup: %w", err)
	}

	if backupExists > 0 {
		// 重新创建services表结构
		_, err = tx.Exec(`
			CREATE TABLE IF NOT EXISTS services (
				id bigint unsigned NOT NULL AUTO_INCREMENT,
				created_at datetime(3) DEFAULT NULL,
				updated_at datetime(3) DEFAULT NULL,
				deleted_at datetime(3) DEFAULT NULL,
				title varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
				description text COLLATE utf8mb4_unicode_ci NOT NULL,
				icon varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
				order bigint DEFAULT '0',
				PRIMARY KEY (id),
				KEY idx_services_deleted_at (deleted_at)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
		`)
		if err != nil {
			return fmt.Errorf("failed to recreate services table: %w", err)
		}

		// 恢复数据
		_, err = tx.Exec("INSERT INTO services SELECT * FROM services_backup_goose")
		if err != nil {
			return fmt.Errorf("failed to restore services data: %w", err)
		}

		// 删除备份表
		_, err = tx.Exec("DROP TABLE services_backup_goose")
		if err != nil {
			return fmt.Errorf("failed to drop services backup: %w", err)
		}
	}

	return nil
}
